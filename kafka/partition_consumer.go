package kafka

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"

	"traffic-mirror/config"
	"traffic-mirror/logger"
)

func partitionsToString(partitions []int) string {
	strs := make([]string, len(partitions))
	for i, p := range partitions {
		strs[i] = strconv.Itoa(p)
	}
	return "[" + fmt.Sprintf("%s", join(strs, ",")) + "]"
}

func join(strs []string, sep string) string {
	result := ""
	for i, s := range strs {
		if i > 0 {
			result += sep
		}
		result += s
	}
	return result
}

// NewPartitionConsumer 创建分区专用消费者
func NewPartitionConsumer(cfg *config.Config, logManager *logger.LoggerManager, handler MessageHandler, partitions []int) (*Consumer, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 获取系统日志器用于Kafka内部日志
	systemLogger := logManager.GetLogger(logger.LogTypeSystem)

	// 确定消费起始位置策略
	startOffset := getConsumerStartOffset(cfg.Kafka.Consumer.OffsetInitial)

	// 创建Kafka Reader配置 - 使用GroupID模式（不指定分区）
	readerConfig := kafka.ReaderConfig{
		Brokers: cfg.Kafka.Brokers,
		Topic:   cfg.Kafka.Topic,
		GroupID: cfg.Kafka.GroupID + partitionsToString(partitions), // 使用传入的专用GroupID，让Kafka自动分配分区

		// 实时消费且无遗漏策略
		StartOffset:       startOffset,
		MinBytes:          1e3,                    // 1KB
		MaxBytes:          10e6,                   // 10MB
		MaxWait:           100 * time.Millisecond, // 100ms
		HeartbeatInterval: 3 * time.Second,
		SessionTimeout:    30 * time.Second,
		CommitInterval:    500 * time.Millisecond, // 频繁提交
		ReadBackoffMin:    50 * time.Millisecond,
		ReadBackoffMax:    500 * time.Millisecond,

		// 连接配置
		Dialer: &kafka.Dialer{
			Timeout:   10 * time.Second,
			DualStack: true,
		},

		// 错误处理
		ErrorLogger: kafka.LoggerFunc(func(msg string, args ...interface{}) {
			systemLogger.Debugf("Kafka partition reader: "+msg, args...)
		}),
	}

	reader := kafka.NewReader(readerConfig)

	// 记录分区分配信息
	systemLogger.WithFields(logrus.Fields{
		"group_id":            cfg.Kafka.GroupID,
		"topic":               cfg.Kafka.Topic,
		"assigned_partitions": partitions,
		"offset_strategy":     cfg.Kafka.Consumer.OffsetInitial,
		"start_offset":        startOffset,
	}).Info("Partition consumer configured")

	// 使用配置中的并行工作数
	parallelWorkers := cfg.Kafka.ParallelWorkers
	if parallelWorkers <= 0 {
		parallelWorkers = 10 // 默认10个worker
	}

	consumer := &Consumer{
		config:            cfg,
		logManager:        logManager,
		Reader:            reader,
		ctx:               ctx,
		cancel:            cancel,
		messageHandler:    handler,
		parallelWorkers:   parallelWorkers,
		processingTimeout: 5 * time.Second,
		messageChan:       make(chan *Message, parallelWorkers*2),
		workerPool:        make(chan struct{}, parallelWorkers),

		// 初始化增强字段
		pendingMessages: make(map[string]*PendingMessage),
		retryQueue:      make(chan *Message, parallelWorkers),
		commitChan:      make(chan kafka.Message, parallelWorkers*2),
		lastCommitTime:  time.Now(),
		metrics: &ConsumerMetrics{
			LastUpdateTime: time.Now(),
		},
		metricsInterval: time.Duration(cfg.App.MetricsInterval) * time.Second,
	}

	return consumer, nil
}

// MultiPartitionConsumer 多分区消费者（如果需要一个消费者处理多个分区）
type MultiPartitionConsumer struct {
	consumers  []*Consumer
	ctx        context.Context
	cancel     context.CancelFunc
	logManager *logger.LoggerManager
}

// NewMultiPartitionConsumer 创建多分区消费者
func NewMultiPartitionConsumer(cfg *config.Config, logManager *logger.LoggerManager, handler MessageHandler, partitions []int) (*MultiPartitionConsumer, error) {
	ctx, cancel := context.WithCancel(context.Background())

	mpc := &MultiPartitionConsumer{
		ctx:        ctx,
		cancel:     cancel,
		logManager: logManager,
	}

	// 为每个分区创建一个消费者
	for i, partition := range partitions {
		// 创建分区专用配置
		partitionConfig := *cfg
		partitionConfig.Kafka.GroupID = fmt.Sprintf("%s-p%d", cfg.Kafka.GroupID, partition)
		partitionConfig.Kafka.ParallelWorkers = cfg.Kafka.ParallelWorkers / len(partitions)
		if partitionConfig.Kafka.ParallelWorkers == 0 {
			partitionConfig.Kafka.ParallelWorkers = 1
		}

		consumer, err := NewPartitionConsumer(&partitionConfig, logManager, handler, []int{partition})
		if err != nil {
			// 清理已创建的消费者
			for _, c := range mpc.consumers {
				c.Stop()
			}
			cancel()
			return nil, fmt.Errorf("failed to create consumer for partition %d: %w", partition, err)
		}

		mpc.consumers = append(mpc.consumers, consumer)

		logManager.LogSystem(logrus.DebugLevel, logrus.Fields{
			"partition":   partition,
			"consumer_id": i,
			"workers":     partitionConfig.Kafka.ParallelWorkers,
		}, "Partition consumer created")
	}

	return mpc, nil
}

// Start 启动多分区消费者
func (mpc *MultiPartitionConsumer) Start() error {
	mpc.logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
		"partition_consumers": len(mpc.consumers),
	}, "Starting multi-partition consumer")

	for i, consumer := range mpc.consumers {
		if err := consumer.Start(); err != nil {
			// 停止已启动的消费者
			for j := 0; j < i; j++ {
				mpc.consumers[j].Stop()
			}
			return fmt.Errorf("failed to start partition consumer %d: %w", i, err)
		}
	}

	mpc.logManager.LogSystem(logrus.InfoLevel, nil, "Multi-partition consumer started successfully")
	return nil
}

// Stop 停止多分区消费者
func (mpc *MultiPartitionConsumer) Stop() error {
	mpc.logManager.LogSystem(logrus.InfoLevel, nil, "Stopping multi-partition consumer")

	mpc.cancel()

	for i, consumer := range mpc.consumers {
		if err := consumer.Stop(); err != nil {
			mpc.logManager.LogError(logrus.ErrorLevel, logrus.Fields{
				"consumer_id": i,
				"error":       err.Error(),
			}, "Error stopping partition consumer")
		}
	}

	mpc.logManager.LogSystem(logrus.InfoLevel, nil, "Multi-partition consumer stopped")
	return nil
}

// GetAggregatedMetrics 获取聚合性能指标
func (mpc *MultiPartitionConsumer) GetAggregatedMetrics() *ConsumerMetrics {
	aggregated := &ConsumerMetrics{
		LastUpdateTime: time.Now(),
	}

	for _, consumer := range mpc.consumers {
		metrics := consumer.GetMetrics()
		aggregated.TotalMessages += metrics.TotalMessages
		aggregated.ProcessedMessages += metrics.ProcessedMessages
		aggregated.FailedMessages += metrics.FailedMessages
		aggregated.RetriedMessages += metrics.RetriedMessages
		aggregated.PendingMessages += metrics.PendingMessages
		aggregated.ThroughputPerSec += metrics.ThroughputPerSec

		// 平均延迟取最大值
		if metrics.AverageLatency > aggregated.AverageLatency {
			aggregated.AverageLatency = metrics.AverageLatency
		}
	}

	return aggregated
}
