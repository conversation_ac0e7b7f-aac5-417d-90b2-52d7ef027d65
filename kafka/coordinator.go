package kafka

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"

	"traffic-mirror/config"
	"traffic-mirror/logger"
)

// ConsumerCoordinator 多消费者协调器
type ConsumerCoordinator struct {
	config     *config.Config
	logManager *logger.LoggerManager
	consumers  []*Consumer
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup

	// 分区分配
	partitions    []int
	consumerCount int

	// 性能监控
	totalMetrics *ConsumerMetrics
	metricsLock  sync.RWMutex
}

// NewConsumerCoordinator 创建消费者协调器
func NewConsumerCoordinator(cfg *config.Config, logManager *logger.LoggerManager, handler MessageHandler) (*ConsumerCoordinator, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 获取主题分区信息
	partitions, err := getTopicPartitions(cfg.Kafka.Brokers, cfg.Kafka.Topic)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to get topic partitions: %w", err)
	}

	// 计算消费者数量（不超过分区数）
	consumerCount := cfg.Kafka.ParallelWorkers / 10 // 每10个worker一个消费者
	if consumerCount == 0 {
		consumerCount = 1
	}
	if consumerCount > len(partitions) {
		consumerCount = len(partitions) // 不能超过分区数
	}

	coordinator := &ConsumerCoordinator{
		config:        cfg,
		logManager:    logManager,
		ctx:           ctx,
		cancel:        cancel,
		partitions:    partitions,
		consumerCount: consumerCount,
		totalMetrics: &ConsumerMetrics{
			LastUpdateTime: time.Now(),
		},
	}

	// 创建多个消费者
	for i := 0; i < consumerCount; i++ {
		// 为每个消费者分配特定分区
		assignedPartitions := coordinator.assignPartitions(i)
		logManager.LogSystem(logrus.DebugLevel, logrus.Fields{
			"consumer_id":   i,
			"partitions":    assignedPartitions,
			"total_workers": cfg.Kafka.ParallelWorkers,
		}, "Consumer partitions assigned")
		// 创建专用配置
		consumerConfig := *cfg
		consumerConfig.Kafka.GroupID = fmt.Sprintf("%s-consumer-%d-%s", cfg.Kafka.GroupID, i, time.Now().Format("20060102150405"))
		consumerConfig.Kafka.ParallelWorkers = cfg.Kafka.ParallelWorkers / consumerCount

		consumer, err := NewPartitionConsumer(&consumerConfig, logManager, handler, assignedPartitions)
		if err != nil {
			coordinator.Stop()
			return nil, fmt.Errorf("failed to create consumer %d: %w", i, err)
		}

		coordinator.consumers = append(coordinator.consumers, consumer)
	}

	logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
		"consumer_count": consumerCount,
		"partitions":     len(partitions),
		"topic":          cfg.Kafka.Topic,
	}, "Consumer coordinator created")

	return coordinator, nil
}

// getTopicPartitions 获取主题分区信息
func getTopicPartitions(brokers []string, topic string) ([]int, error) {
	conn, err := kafka.Dial("tcp", brokers[0])
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	partitions, err := conn.ReadPartitions(topic)
	if err != nil {
		return nil, err
	}

	var partitionIDs []int
	for _, p := range partitions {
		partitionIDs = append(partitionIDs, p.ID)
	}

	return partitionIDs, nil
}

// assignPartitions 为消费者分配分区
func (cc *ConsumerCoordinator) assignPartitions(consumerIndex int) []int {
	var assigned []int

	// 平均分配分区
	partitionsPerConsumer := len(cc.partitions) / cc.consumerCount
	remainder := len(cc.partitions) % cc.consumerCount

	start := consumerIndex * partitionsPerConsumer
	end := start + partitionsPerConsumer

	// 处理余数分区
	if consumerIndex < remainder {
		start += consumerIndex
		end += consumerIndex + 1
	} else {
		start += remainder
		end += remainder
	}

	for i := start; i < end && i < len(cc.partitions); i++ {
		assigned = append(assigned, cc.partitions[i])
	}

	return assigned
}

// Start 启动协调器
func (cc *ConsumerCoordinator) Start() error {
	cc.logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
		"consumers": len(cc.consumers),
	}, "Starting consumer coordinator")

	// 启动所有消费者
	for i, consumer := range cc.consumers {
		if err := consumer.Start(); err != nil {
			cc.logManager.LogError(logrus.ErrorLevel, logrus.Fields{
				"consumer_id": i,
				"error":       err.Error(),
			}, "Failed to start consumer")
			return err
		}
	}

	// 启动性能聚合监控
	cc.wg.Add(1)
	go func() {
		defer cc.wg.Done()
		cc.aggregateMetrics()
	}()

	cc.logManager.LogSystem(logrus.InfoLevel, nil, "Consumer coordinator started successfully")
	return nil
}

// Stop 停止协调器
func (cc *ConsumerCoordinator) Stop() error {
	cc.logManager.LogSystem(logrus.InfoLevel, nil, "Stopping consumer coordinator")

	// 取消上下文
	cc.cancel()

	// 停止所有消费者
	for i, consumer := range cc.consumers {
		if err := consumer.Stop(); err != nil {
			cc.logManager.LogError(logrus.ErrorLevel, logrus.Fields{
				"consumer_id": i,
				"error":       err.Error(),
			}, "Error stopping consumer")
		}
	}

	// 等待协程结束
	cc.wg.Wait()

	cc.logManager.LogSystem(logrus.InfoLevel, nil, "Consumer coordinator stopped")
	return nil
}

// aggregateMetrics 聚合所有消费者的性能指标
func (cc *ConsumerCoordinator) aggregateMetrics() {
	ticker := time.NewTicker(time.Duration(cc.config.App.MetricsInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-cc.ctx.Done():
			return
		case <-ticker.C:
			cc.metricsLock.Lock()

			// 重置聚合指标
			cc.totalMetrics = &ConsumerMetrics{
				LastUpdateTime: time.Now(),
			}

			// 聚合所有消费者的指标
			for _, consumer := range cc.consumers {
				metrics := consumer.GetMetrics()
				cc.totalMetrics.TotalMessages += metrics.TotalMessages
				cc.totalMetrics.ProcessedMessages += metrics.ProcessedMessages
				cc.totalMetrics.FailedMessages += metrics.FailedMessages
				cc.totalMetrics.RetriedMessages += metrics.RetriedMessages
				cc.totalMetrics.PendingMessages += metrics.PendingMessages
				cc.totalMetrics.ThroughputPerSec += metrics.ThroughputPerSec

				// 平均延迟取最大值
				if metrics.AverageLatency > cc.totalMetrics.AverageLatency {
					cc.totalMetrics.AverageLatency = metrics.AverageLatency
				}
			}

			cc.metricsLock.Unlock()

			// 记录聚合性能指标
			cc.logManager.LogStats(logrus.InfoLevel, logrus.Fields{
				"total_messages":     cc.totalMetrics.TotalMessages,
				"processed_messages": cc.totalMetrics.ProcessedMessages,
				"failed_messages":    cc.totalMetrics.FailedMessages,
				"retried_messages":   cc.totalMetrics.RetriedMessages,
				"pending_messages":   cc.totalMetrics.PendingMessages,
				"average_latency":    cc.totalMetrics.AverageLatency,
				"throughput_per_sec": cc.totalMetrics.ThroughputPerSec,
				"consumer_count":     len(cc.consumers),
			}, "Multi-consumer coordinator performance metrics")
		}
	}
}

// GetTotalMetrics 获取聚合性能指标
func (cc *ConsumerCoordinator) GetTotalMetrics() *ConsumerMetrics {
	cc.metricsLock.RLock()
	defer cc.metricsLock.RUnlock()

	return &ConsumerMetrics{
		TotalMessages:     cc.totalMetrics.TotalMessages,
		ProcessedMessages: cc.totalMetrics.ProcessedMessages,
		FailedMessages:    cc.totalMetrics.FailedMessages,
		RetriedMessages:   cc.totalMetrics.RetriedMessages,
		PendingMessages:   cc.totalMetrics.PendingMessages,
		AverageLatency:    cc.totalMetrics.AverageLatency,
		ThroughputPerSec:  cc.totalMetrics.ThroughputPerSec,
		LastUpdateTime:    cc.totalMetrics.LastUpdateTime,
	}
}
