package kafka

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"

	"traffic-mirror/config"
	"traffic-mirror/logger"
)

// Consumer Kafka消费者 - 增强版本
type Consumer struct {
	config            *config.Config
	logManager        *logger.LoggerManager
	Reader            *kafka.Reader
	ctx               context.Context
	cancel            context.CancelFunc
	wg                sync.WaitGroup
	messageHandler    MessageHandler
	processingTimeout time.Duration // 处理超时配置
	parallelWorkers   int           // 并行工作数
	messageChan       chan *Message // 消息通道
	workerPool        chan struct{} // 工作池

	// 新增：消息确认和重试机制
	pendingMessages map[string]*PendingMessage // 待确认消息
	retryQueue      chan *Message              // 重试队列
	pendingMutex    sync.RWMutex               // 保护待确认消息的锁
	commitChan      chan kafka.Message         // 提交通道
	lastCommitTime  time.Time                  // 上次提交时间

	// 新增：性能监控
	metrics         *ConsumerMetrics
	metricsInterval time.Duration
}

// MessageHandler 消息处理器接口
type MessageHandler interface {
	HandleMessage(consumer *Consumer, message *Message) error
}

// Message 消息结构
type Message struct {
	Topic     string
	Partition int
	Offset    int64
	Key       []byte
	Value     []byte
	Time      time.Time
	// 新增：消息追踪信息
	MessageID   string        // 消息唯一ID
	RetryCount  int           // 重试次数
	OriginalMsg kafka.Message // 原始Kafka消息
}

// PendingMessage 待确认消息
type PendingMessage struct {
	Message    *Message
	StartTime  time.Time
	RetryCount int
	LastRetry  time.Time
}

// ConsumerMetrics 消费者性能指标
type ConsumerMetrics struct {
	TotalMessages     int64     `json:"total_messages"`
	ProcessedMessages int64     `json:"processed_messages"`
	FailedMessages    int64     `json:"failed_messages"`
	RetriedMessages   int64     `json:"retried_messages"`
	PendingMessages   int64     `json:"pending_messages"`
	AverageLatency    int64     `json:"average_latency_ms"`
	ThroughputPerSec  int64     `json:"throughput_per_sec"`
	LastUpdateTime    time.Time `json:"last_update_time"`
}

// generateMessageID 生成消息唯一ID
func generateMessageID(topic string, partition int, offset int64) string {
	return fmt.Sprintf("%s-%d-%d", topic, partition, offset)
}

// getConsumerStartOffset 获取消费者起始位置
func getConsumerStartOffset(offsetStrategy string) int64 {
	switch offsetStrategy {
	case "earliest":
		return kafka.FirstOffset // 从最早消息开始（会消费所有历史消息）
	case "latest":
		return kafka.LastOffset // 从最新消息开始（会遗漏启动前消息）
	case "newest":
		// 推荐策略：从最新开始，但使用固定GroupID确保重启后连续性
		// 这样既避免了大量历史消息，又保证了重启后的连续性
		return kafka.LastOffset
	default:
		return kafka.LastOffset
	}
}

// NewConsumer 创建新的Kafka消费者
func NewConsumer(cfg *config.Config, logManager *logger.LoggerManager, handler MessageHandler) (*Consumer, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 获取系统日志器用于Kafka内部日志
	systemLogger := logManager.GetLogger(logger.LogTypeSystem)

	// 确定消费起始位置策略
	startOffset := getConsumerStartOffset(cfg.Kafka.Consumer.OffsetInitial)

	// 创建Kafka Reader配置 - 实时消费且无遗漏策略
	readerConfig := kafka.ReaderConfig{
		Brokers: cfg.Kafka.Brokers,
		Topic:   cfg.Kafka.Topic,
		// GroupID: cfg.Kafka.GroupID + "_" + time.Now().Format("20060102150405")
		GroupID: cfg.Kafka.GroupID,

		// 实时消费且无遗漏策略：
		// 1. 首次启动：从最新位置开始（避免历史消息堆积）
		// 2. 重启后：从上次提交的offset继续（保证无遗漏）
		StartOffset:       startOffset,
		MinBytes:          1e3,                    // 降低到1KB，减少等待时间
		MaxBytes:          10e6,                   // 10MB批量大小
		MaxWait:           100 * time.Millisecond, // 缩短等待时间到100ms
		HeartbeatInterval: 3 * time.Second,        // 心跳间隔
		SessionTimeout:    30 * time.Second,       // 会话超时
		CommitInterval:    500 * time.Millisecond, // 更频繁的提交，减少重复消费和遗漏
		ReadBackoffMin:    50 * time.Millisecond,  // 更短的重试间隔
		ReadBackoffMax:    500 * time.Millisecond,

		// 连接和读取超时
		Dialer: &kafka.Dialer{
			Timeout:   10 * time.Second, // 缩短连接超时
			DualStack: true,
		},

		// 错误处理
		ErrorLogger: kafka.LoggerFunc(func(msg string, args ...interface{}) {
			systemLogger.Debugf("Kafka reader: "+msg, args...)
		}),
	}

	reader := kafka.NewReader(readerConfig)

	// 记录offset策略信息
	systemLogger.WithFields(logrus.Fields{
		"group_id":        readerConfig.GroupID,
		"topic":           cfg.Kafka.Topic,
		"offset_strategy": cfg.Kafka.Consumer.OffsetInitial,
		"start_offset":    startOffset,
	}).Info("Kafka consumer offset strategy configured")

	// 使用配置中的并行工作数
	parallelWorkers := cfg.Kafka.ParallelWorkers
	if parallelWorkers <= 0 {
		parallelWorkers = 10 // 默认10个worker
	}

	consumer := &Consumer{
		config:            cfg,
		logManager:        logManager,
		Reader:            reader,
		ctx:               ctx,
		cancel:            cancel,
		messageHandler:    handler,
		parallelWorkers:   parallelWorkers,
		processingTimeout: 5 * time.Second,
		messageChan:       make(chan *Message, parallelWorkers*2), // 缓冲通道
		workerPool:        make(chan struct{}, parallelWorkers),   // 工作池

		// 初始化新增字段
		pendingMessages: make(map[string]*PendingMessage),
		retryQueue:      make(chan *Message, parallelWorkers),        // 重试队列
		commitChan:      make(chan kafka.Message, parallelWorkers*2), // 提交通道
		lastCommitTime:  time.Now(),
		metrics: &ConsumerMetrics{
			LastUpdateTime: time.Now(),
		},
		metricsInterval: time.Duration(cfg.App.MetricsInterval) * time.Second,
	}

	return consumer, nil
}

// checkOffsetStatus 检查offset状态，确保无遗漏消费
func (c *Consumer) checkOffsetStatus() {
	// 获取当前分区信息
	conn, err := kafka.DialLeader(context.Background(), "tcp", c.config.Kafka.Brokers[0], c.config.Kafka.Topic, 0)
	if err != nil {
		c.logManager.LogSystem(logrus.WarnLevel, logrus.Fields{"error": err.Error()}, "Failed to connect to Kafka for offset check")
		return
	}
	defer conn.Close()

	// 获取最新offset
	latestOffset, err := conn.ReadLastOffset()
	if err != nil {
		c.logManager.LogSystem(logrus.WarnLevel, logrus.Fields{"error": err.Error()}, "Failed to read latest offset")
		return
	}

	// 记录offset状态
	c.logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
		"topic":         c.config.Kafka.Topic,
		"group_id":      c.Reader.Config().GroupID,
		"latest_offset": latestOffset,
		"strategy":      c.config.Kafka.Consumer.OffsetInitial,
	}, "Kafka offset status check")
}

// Start 启动消费者
func (c *Consumer) Start() error {
	c.logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
		"brokers":          c.config.Kafka.Brokers,
		"topic":            c.config.Kafka.Topic,
		"group":            c.Reader.Config().GroupID,
		"parallel_workers": c.parallelWorkers,
	}, "Starting Kafka consumer")

	// 检查offset状态，确保无遗漏消费
	c.checkOffsetStatus()

	// 启动消息读取协程
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()
		c.readMessages()
	}()

	// 启动多个worker协程处理消息
	for i := 0; i < c.parallelWorkers; i++ {
		c.wg.Add(1)
		go func(workerID int) {
			defer c.wg.Done()
			c.processMessages(workerID)
		}(i)
	}

	// 启动重试处理协程
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()
		c.processRetryQueue()
	}()

	// 启动提交处理协程
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()
		c.processCommits()
	}()

	// 启动性能监控协程
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()
		c.monitorPerformance()
	}()

	// 启动待确认消息清理协程
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()
		c.cleanupPendingMessages()
	}()

	c.logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
		"workers": c.parallelWorkers,
	}, "Kafka consumer started successfully")
	return nil
}

// readMessages 读取消息并分发到worker
func (c *Consumer) readMessages() {
	defer close(c.messageChan) // 关闭通道，通知worker退出

	for {
		select {
		case <-c.ctx.Done():
			c.logManager.LogSystem(logrus.InfoLevel, nil, "Consumer context cancelled, stopping message reading")
			return
		default:
			// 读取消息
			kafkaMsg, err := c.Reader.ReadMessage(c.ctx)
			if err != nil {
				if c.ctx.Err() != nil {
					// 上下文已取消，正常退出
					return
				}

				// 过滤常见的网络错误
				errMsg := err.Error()
				if strings.Contains(errMsg, "broken pipe") ||
					strings.Contains(errMsg, "EOF") ||
					strings.Contains(errMsg, "connection reset") ||
					strings.Contains(errMsg, "timeout") {
					c.logManager.LogSystem(logrus.DebugLevel, logrus.Fields{"error": err.Error()}, "Network error reading message (will retry)")
				} else {
					c.logManager.LogError(logrus.ErrorLevel, logrus.Fields{"error": err.Error()}, "Error reading message")
				}

				// 等待一段时间后重试
				time.Sleep(100 * time.Millisecond) // 缩短重试间隔
				continue
			}

			// 转换消息格式，增加追踪信息
			message := &Message{
				Topic:       kafkaMsg.Topic,
				Partition:   kafkaMsg.Partition,
				Offset:      kafkaMsg.Offset,
				Key:         kafkaMsg.Key,
				Value:       kafkaMsg.Value,
				Time:        kafkaMsg.Time,
				MessageID:   generateMessageID(kafkaMsg.Topic, kafkaMsg.Partition, kafkaMsg.Offset),
				RetryCount:  0,
				OriginalMsg: kafkaMsg,
			}

			// 更新总消息计数
			atomic.AddInt64(&c.metrics.TotalMessages, 1)

			// 记录接收到的消息
			c.logManager.LogRequest(logrus.DebugLevel, logrus.Fields{
				"topic":     message.Topic,
				"partition": message.Partition,
				"offset":    message.Offset,
				"timestamp": message.Time,
			}, "Received message")

			// 发送消息到worker通道
			select {
			case c.messageChan <- message:
				// 消息成功发送到worker
			case <-c.ctx.Done():
				return
			}
		}
	}
}

// processMessages worker处理消息
func (c *Consumer) processMessages(workerID int) {
	c.logManager.LogSystem(logrus.DebugLevel, logrus.Fields{
		"worker_id": workerID,
	}, "Message worker started")

	for {
		select {
		case message, ok := <-c.messageChan:
			if !ok {
				// 通道已关闭，退出worker
				c.logManager.LogSystem(logrus.DebugLevel, logrus.Fields{
					"worker_id": workerID,
				}, "Message worker stopped")
				return
			}

			// 处理消息
			c.handleSingleMessage(message, workerID)

		case <-c.ctx.Done():
			c.logManager.LogSystem(logrus.DebugLevel, logrus.Fields{
				"worker_id": workerID,
			}, "Message worker context cancelled")
			return
		}
	}
}

// handleSingleMessage 处理单条消息 - 增强版本
func (c *Consumer) handleSingleMessage(message *Message, workerID int) {
	startTime := time.Now()

	// 添加到待确认消息列表
	c.addPendingMessage(message)

	// 使用超时上下文处理消息
	ctx, cancel := context.WithTimeout(c.ctx, c.processingTimeout)
	defer cancel()

	// 在goroutine中处理消息，支持超时
	done := make(chan error, 1)
	go func() {
		done <- c.messageHandler.HandleMessage(c, message)
	}()

	select {
	case err := <-done:
		// 计算处理延迟
		latency := time.Since(startTime).Milliseconds()

		if err != nil {
			// 处理失败，加入重试队列
			atomic.AddInt64(&c.metrics.FailedMessages, 1)
			c.logManager.LogError(logrus.WarnLevel, logrus.Fields{
				"error":      err.Error(),
				"message_id": message.MessageID,
				"worker_id":  workerID,
				"latency":    latency,
			}, "Message processing failed, will retry")

			// 增加重试计数并加入重试队列
			message.RetryCount++
			select {
			case c.retryQueue <- message:
				atomic.AddInt64(&c.metrics.RetriedMessages, 1)
			default:
				// 重试队列满，记录错误但继续处理
				c.logManager.LogError(logrus.ErrorLevel, logrus.Fields{
					"message_id": message.MessageID,
				}, "Retry queue full, message dropped")
			}
		} else {
			// 处理成功，标记为可提交
			atomic.AddInt64(&c.metrics.ProcessedMessages, 1)
			c.markMessageForCommit(message)

			c.logManager.LogRequest(logrus.DebugLevel, logrus.Fields{
				"message_id": message.MessageID,
				"worker_id":  workerID,
				"latency":    latency,
			}, "Message processed successfully")
		}

		// 更新平均延迟
		c.updateAverageLatency(latency)

	case <-ctx.Done():
		// 处理超时，加入重试队列
		atomic.AddInt64(&c.metrics.FailedMessages, 1)
		c.logManager.LogError(logrus.WarnLevel, logrus.Fields{
			"message_id": message.MessageID,
			"worker_id":  workerID,
			"timeout":    c.processingTimeout,
		}, "Message processing timeout, will retry")

		message.RetryCount++
		select {
		case c.retryQueue <- message:
			atomic.AddInt64(&c.metrics.RetriedMessages, 1)
		default:
			c.logManager.LogError(logrus.ErrorLevel, logrus.Fields{
				"message_id": message.MessageID,
			}, "Retry queue full after timeout, message dropped")
		}
	}
}

// Stop 停止消费者
func (c *Consumer) Stop() error {
	c.logManager.LogSystem(logrus.InfoLevel, nil, "Stopping Kafka consumer")

	// 取消上下文，通知所有协程停止
	c.cancel()

	// 等待所有协程结束
	c.wg.Wait()

	// 关闭reader
	if err := c.Reader.Close(); err != nil {
		c.logManager.LogError(logrus.ErrorLevel, logrus.Fields{"error": err.Error()}, "Error closing kafka reader")
		return err
	}

	c.logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
		"workers": c.parallelWorkers,
	}, "Kafka consumer stopped")
	return nil
}

// addPendingMessage 添加待确认消息
func (c *Consumer) addPendingMessage(message *Message) {
	c.pendingMutex.Lock()
	defer c.pendingMutex.Unlock()

	c.pendingMessages[message.MessageID] = &PendingMessage{
		Message:    message,
		StartTime:  time.Now(),
		RetryCount: message.RetryCount,
		LastRetry:  time.Now(),
	}

	atomic.AddInt64(&c.metrics.PendingMessages, 1)
}

// markMessageForCommit 标记消息为可提交
func (c *Consumer) markMessageForCommit(message *Message) {
	// 从待确认列表中移除
	c.pendingMutex.Lock()
	if _, exists := c.pendingMessages[message.MessageID]; exists {
		delete(c.pendingMessages, message.MessageID)
		atomic.AddInt64(&c.metrics.PendingMessages, -1)
	}
	c.pendingMutex.Unlock()

	// 发送到提交通道
	select {
	case c.commitChan <- message.OriginalMsg:
		// 成功发送到提交通道
	default:
		// 提交通道满，记录警告
		c.logManager.LogError(logrus.WarnLevel, logrus.Fields{
			"message_id": message.MessageID,
		}, "Commit channel full, message may be reprocessed")
	}
}

// updateAverageLatency 更新平均延迟
func (c *Consumer) updateAverageLatency(latency int64) {
	// 简单的移动平均算法
	currentAvg := atomic.LoadInt64(&c.metrics.AverageLatency)
	newAvg := (currentAvg + latency) / 2
	atomic.StoreInt64(&c.metrics.AverageLatency, newAvg)
}

// processRetryQueue 处理重试队列
func (c *Consumer) processRetryQueue() {
	c.logManager.LogSystem(logrus.InfoLevel, nil, "Retry queue processor started")

	ticker := time.NewTicker(1 * time.Second) // 每秒检查一次重试队列
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			c.logManager.LogSystem(logrus.InfoLevel, nil, "Retry queue processor stopped")
			return

		case message := <-c.retryQueue:
			// 检查重试次数限制
			maxRetries := 3 // 最大重试3次
			if message.RetryCount > maxRetries {
				c.logManager.LogError(logrus.ErrorLevel, logrus.Fields{
					"message_id":  message.MessageID,
					"retry_count": message.RetryCount,
				}, "Message exceeded max retries, dropping")

				// 从待确认列表中移除
				c.pendingMutex.Lock()
				delete(c.pendingMessages, message.MessageID)
				atomic.AddInt64(&c.metrics.PendingMessages, -1)
				c.pendingMutex.Unlock()
				continue
			}

			// 等待一段时间后重试
			retryDelay := time.Duration(message.RetryCount) * time.Second
			time.Sleep(retryDelay)

			// 重新发送到处理队列
			select {
			case c.messageChan <- message:
				c.logManager.LogRequest(logrus.DebugLevel, logrus.Fields{
					"message_id":  message.MessageID,
					"retry_count": message.RetryCount,
				}, "Message requeued for retry")
			case <-c.ctx.Done():
				return
			}

		case <-ticker.C:
			// 定期检查超时的待确认消息
			c.checkTimeoutMessages()
		}
	}
}

// checkTimeoutMessages 检查超时的待确认消息
func (c *Consumer) checkTimeoutMessages() {
	c.pendingMutex.Lock()
	defer c.pendingMutex.Unlock()

	timeout := 30 * time.Second // 30秒超时
	now := time.Now()

	for messageID, pending := range c.pendingMessages {
		if now.Sub(pending.StartTime) > timeout {
			// 消息处理超时，加入重试队列
			c.logManager.LogError(logrus.WarnLevel, logrus.Fields{
				"message_id": messageID,
				"timeout":    timeout,
			}, "Message processing timeout, adding to retry queue")

			pending.Message.RetryCount++
			select {
			case c.retryQueue <- pending.Message:
				atomic.AddInt64(&c.metrics.RetriedMessages, 1)
			default:
				c.logManager.LogError(logrus.ErrorLevel, logrus.Fields{
					"message_id": messageID,
				}, "Retry queue full for timeout message")
			}

			// 从待确认列表中移除
			delete(c.pendingMessages, messageID)
			atomic.AddInt64(&c.metrics.PendingMessages, -1)
		}
	}
}

// processCommits 处理消息提交
func (c *Consumer) processCommits() {
	c.logManager.LogSystem(logrus.InfoLevel, nil, "Commit processor started")

	commitBatch := make([]kafka.Message, 0, 100)     // 批量提交
	ticker := time.NewTicker(500 * time.Millisecond) // 每500ms提交一次
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			// 退出前提交剩余消息
			if len(commitBatch) > 0 {
				c.commitBatch(commitBatch)
			}
			c.logManager.LogSystem(logrus.InfoLevel, nil, "Commit processor stopped")
			return

		case msg := <-c.commitChan:
			commitBatch = append(commitBatch, msg)

			// 批量大小达到限制时立即提交
			if len(commitBatch) >= 100 {
				c.commitBatch(commitBatch)
				commitBatch = commitBatch[:0] // 清空切片
			}

		case <-ticker.C:
			// 定时提交
			if len(commitBatch) > 0 {
				c.commitBatch(commitBatch)
				commitBatch = commitBatch[:0] // 清空切片
			}
		}
	}
}

// commitBatch 批量提交消息
func (c *Consumer) commitBatch(messages []kafka.Message) {
	if len(messages) == 0 {
		return
	}

	// 检查是否使用了GroupID，只有使用GroupID时才能提交offset
	if c.Reader.Config().GroupID == "" {
		// 手动分区分配模式，不需要提交offset
		c.logManager.LogRequest(logrus.DebugLevel, logrus.Fields{
			"message_count": len(messages),
			"mode":          "manual_partition",
		}, "Messages processed (no offset commit needed in manual partition mode)")
		c.lastCommitTime = time.Now()
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := c.Reader.CommitMessages(ctx, messages...)
	if err != nil {
		c.logManager.LogError(logrus.ErrorLevel, logrus.Fields{
			"error":         err.Error(),
			"message_count": len(messages),
		}, "Failed to commit messages")
	} else {
		c.logManager.LogRequest(logrus.DebugLevel, logrus.Fields{
			"message_count": len(messages),
		}, "Messages committed successfully")

		c.lastCommitTime = time.Now()
	}
}

// monitorPerformance 性能监控
func (c *Consumer) monitorPerformance() {
	c.logManager.LogSystem(logrus.InfoLevel, nil, "Performance monitor started")

	ticker := time.NewTicker(c.metricsInterval)
	defer ticker.Stop()

	lastTotal := int64(0)
	lastTime := time.Now()

	for {
		select {
		case <-c.ctx.Done():
			c.logManager.LogSystem(logrus.InfoLevel, nil, "Performance monitor stopped")
			return

		case <-ticker.C:
			now := time.Now()
			currentTotal := atomic.LoadInt64(&c.metrics.TotalMessages)

			// 计算吞吐量
			duration := now.Sub(lastTime).Seconds()
			if duration > 0 {
				throughput := float64(currentTotal-lastTotal) / duration
				atomic.StoreInt64(&c.metrics.ThroughputPerSec, int64(throughput))
			}

			// 更新时间戳
			c.metrics.LastUpdateTime = now
			lastTotal = currentTotal
			lastTime = now

			// 记录性能指标
			c.logManager.LogStats(logrus.InfoLevel, logrus.Fields{
				"total_messages":     atomic.LoadInt64(&c.metrics.TotalMessages),
				"processed_messages": atomic.LoadInt64(&c.metrics.ProcessedMessages),
				"failed_messages":    atomic.LoadInt64(&c.metrics.FailedMessages),
				"retried_messages":   atomic.LoadInt64(&c.metrics.RetriedMessages),
				"pending_messages":   atomic.LoadInt64(&c.metrics.PendingMessages),
				"average_latency":    atomic.LoadInt64(&c.metrics.AverageLatency),
				"throughput_per_sec": atomic.LoadInt64(&c.metrics.ThroughputPerSec),
			}, "Enhanced consumer performance metrics")
		}
	}
}

// cleanupPendingMessages 清理待确认消息
func (c *Consumer) cleanupPendingMessages() {
	c.logManager.LogSystem(logrus.InfoLevel, nil, "Pending messages cleanup started")

	ticker := time.NewTicker(10 * time.Second) // 每10秒清理一次
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			c.logManager.LogSystem(logrus.InfoLevel, nil, "Pending messages cleanup stopped")
			return

		case <-ticker.C:
			c.checkTimeoutMessages()
		}
	}
}

// GetMetrics 获取消费者性能指标
func (c *Consumer) GetMetrics() *ConsumerMetrics {
	return &ConsumerMetrics{
		TotalMessages:     atomic.LoadInt64(&c.metrics.TotalMessages),
		ProcessedMessages: atomic.LoadInt64(&c.metrics.ProcessedMessages),
		FailedMessages:    atomic.LoadInt64(&c.metrics.FailedMessages),
		RetriedMessages:   atomic.LoadInt64(&c.metrics.RetriedMessages),
		PendingMessages:   atomic.LoadInt64(&c.metrics.PendingMessages),
		AverageLatency:    atomic.LoadInt64(&c.metrics.AverageLatency),
		ThroughputPerSec:  atomic.LoadInt64(&c.metrics.ThroughputPerSec),
		LastUpdateTime:    c.metrics.LastUpdateTime,
	}
}
