package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"traffic-mirror/config"
	"traffic-mirror/handler"
	"traffic-mirror/kafka"
	"traffic-mirror/logger"
)

func main() {
	fmt.Println("=== Testing Message Handler ===")

	// 加载配置
	cfg, err := config.LoadConfig("../test_config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置日志
	log, err := logger.SetupLogger(cfg)
	if err != nil {
		log.Fatalf("Failed to setup logger: %v", err)
	}

	// 创建消息处理器
	messageHandler, err := handler.NewMessageHandler(cfg, log, nil)
	if err != nil {
		log.Fatalf("Failed to create message handler: %v", err)
	}

	// 读取测试数据
	data, err := os.ReadFile("../demo_test.json")
	if err != nil {
		log.Fatalf("Failed to read demo_test.json: %v", err)
	}

	fmt.Printf("Input data size: %d bytes\n", len(data))

	// 创建模拟的Kafka消息
	kafkaMessage := &kafka.Message{
		Topic:     cfg.Kafka.Topic,
		Partition: 0,
		Offset:    12345,
		Key:       []byte("test-key"),
		Value:     data,
		Time:      time.Now(),
	}

	fmt.Println("\n=== Processing Message ===")
	fmt.Printf("Topic: %s\n", kafkaMessage.Topic)
	fmt.Printf("Partition: %d\n", kafkaMessage.Partition)
	fmt.Printf("Offset: %d\n", kafkaMessage.Offset)

	// 处理消息
	if err := messageHandler.HandleMessage(nil, kafkaMessage); err != nil {
		log.Fatalf("Failed to handle message: %v", err)
	}

	fmt.Println("Message processed successfully!")

	// 等待一下让异步操作完成
	time.Sleep(100 * time.Millisecond)

	// 获取统计信息
	fmt.Println("\n=== Handler Statistics ===")
	messageHandler.PrintStats()

	// 关闭处理器
	messageHandler.Close()
}
