# 流量镜像工具简化配置文件 - 优化消费速率版本
# 大部分配置可以通过环境变量覆盖

# Kafka配置 (可通过环境变量覆盖)
kafka:
  # 环境变量: KAFKA_BROKERS (逗号分隔)
  brokers:
    - "kafka-0.kafka-bj02-1g32f5.svc.bjb.ipaas.cn:9092"
    - "kafka-1.kafka-bj02-1g32f5.svc.bjb.ipaas.cn:9092"
    - "kafka-2.kafka-bj02-1g32f5.svc.bjb.ipaas.cn:9092"
    # - "************:19092"
  # 环境变量: KAFKA_TOPIC
  topic: "lynxiao_flow"
  # 环境变量: KAFKA_GROUP_ID
  group_id: "traffic-mirror"
  # 环境变量: KAFKA_PARALLEL_WORKERS
  parallel_workers: 20  # 并行工作数，提高消费速率

  # 多消费者配置 (高级功能)
  multi_consumer:
    # 环境变量: KAFKA_MULTI_CONSUMER_ENABLED
    enabled: true  # 启用多消费者模式，避免重复消费
    # 环境变量: KAFKA_MULTI_CONSUMER_COUNT
    consumer_count: 6  # 消费者数量，0表示自动计算（基于分区数）

  consumer:
    offset_initial: "newest"
    session_timeout: 30
    heartbeat_interval: 3

# 目标API配置 (可通过环境变量覆盖)
target_api:
  # 环境变量: TARGET_API_URL
  url: "http://*************:31011/v1/search"
  # 环境变量: TARGET_API_TIMEOUT (秒)
  timeout: 5  # 缩短超时时间，提高处理速度

# 过滤配置 (可通过环境变量覆盖)
filter:
  # 环境变量: MESSAGE_TYPE
  message_type: "SearchAPI-Request"

# 日志配置 (简化版本)
log:
  # 环境变量: LOG_LEVEL
  level: "info"
  format: "json"
  
  # 请求日志 - 记录到文件
  request:
    enabled: true
    output: "file"
    file_path: "./logs/request.log"
    level: "info"
  
  # 统计日志 - 输出到控制台
  stats:
    enabled: true
    output: "stdout"
    level: "info"
  
  # 错误日志 - 输出到文件和控制台
  error:
    enabled: true
    output: "both"
    file_path: "./logs/error.log"
    level: "error"
  
  # 系统日志 - 输出到控制台
  system:
    enabled: true
    output: "stdout"
    level: "info"

# 应用配置
app:
  name: "traffic-mirror"
  version: "1.0.0"
  enable_metrics: true
  metrics_interval: 30  # 缩短统计间隔，更频繁的性能监控
