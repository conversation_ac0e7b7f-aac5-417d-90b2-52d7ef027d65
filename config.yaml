# 流量镜像工具配置文件
kafka:
  # Kafka集群地址
  brokers:
    - "10.101.1.105:19092"
  # 消费的topic名称
  topic: "lynxiao_flow"
  # 消费者组ID
  group_id: "traffic-mirror-consumer"
  # 消费者配置
  consumer:
    # 从最新位置开始消费（不消费历史消息）
    offset_initial: "newest"
    # 会话超时时间（秒）
    session_timeout: 30
    # 心跳间隔（秒）
    heartbeat_interval: 3

# 目标API配置
target_api:
  # 目标API地址
  url: "http://0.0.0.0:50409/v1/search"
  # 请求超时时间（秒）
  timeout: 30
  # 重试次数
  retry_count: 3
  # 重试间隔（秒）
  retry_interval: 1

# 过滤配置
filter:
  # 需要处理的消息类型
  message_type: "SearchAPI-Request"

# 日志配置
log:
  # 日志级别: debug, info, warn, error
  level: "info"
  # 日志格式: json, text
  format: "json"
  # 日志输出: stdout, file
  output: "stdout"
  # 日志文件路径（当output为file时使用）
  file_path: "./logs/traffic-mirror.log"

# 应用配置
app:
  # 应用名称
  name: "traffic-mirror"
  # 版本
  version: "1.0.0"
  # 是否启用性能监控
  enable_metrics: true
  # 监控统计间隔（秒）
  metrics_interval: 60
