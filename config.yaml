# 流量镜像工具配置文件
kafka:
  # Kafka集群地址
  brokers:
    - "10.101.1.105:19092"
  # 消费的topic名称
  topic: "lynxiao_flow"
  # 消费者组ID
  group_id: "traffic-mirror-consumer"
  # 并行工作数
  parallel_workers: 20
  # 多消费者配置（修复重复消费问题）
  multi_consumer:
    enabled: true  # 启用多消费者模式，避免重复消费
    consumer_count: 0  # 0表示自动根据分区数创建消费者
  # 消费者配置
  consumer:
    # 从最新位置开始消费（不消费历史消息）
    offset_initial: "newest"
    # 会话超时时间（秒）
    session_timeout: 30
    # 心跳间隔（秒）
    heartbeat_interval: 3

# 目标API配置
target_api:
  # 目标API地址
  url: "http://10.103.240.54:31011/v1/search"
  # 请求超时时间（秒）
  timeout: 30
  # 重试次数
  retry_count: 3
  # 重试间隔（秒）
  retry_interval: 1

# 过滤配置
filter:
  # 需要处理的消息类型
  message_type: "SearchAPI-Request"

# 日志配置
log:
  # 日志级别: debug, info, warn, error
  level: "info"
  # 日志格式: json, text
  format: "json"

  # 请求日志配置
  request:
    enabled: true
    output: "file"
    file_path: "./logs/request.log"
    level: "info"

  # 统计日志配置
  stats:
    enabled: true
    output: "stdout"
    level: "info"

  # 错误日志配置
  error:
    enabled: true
    output: "both"
    file_path: "./logs/error.log"
    level: "error"

  # 系统日志配置
  system:
    enabled: true
    output: "stdout"
    level: "info"

# 应用配置
app:
  # 应用名称
  name: "traffic-mirror"
  # 版本
  version: "1.0.0"
  # 是否启用性能监控
  enable_metrics: true
  # 监控统计间隔（秒）
  metrics_interval: 60
